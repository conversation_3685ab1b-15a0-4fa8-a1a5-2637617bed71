-- Activity System Optimized Schema with Partitioning and Indexing
-- Created for high-performance activity tracking and reward processing

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_partman";

-- =====================================================
-- Activity Events Table (Partitioned by time)
-- =====================================================
CREATE TABLE "public"."activity_events" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "created_at" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "event_type" varchar(20) NOT NULL,
  "external_ref" varchar(255) NOT NULL,
  "idempotency_key" varchar(255) NOT NULL,
  "user_id" uuid NOT NULL,
  "user_address" varchar(100) NOT NULL,
  "event_data" jsonb,
  "volume" decimal(38,18) NOT NULL DEFAULT 0,
  "quantity" bigint NOT NULL DEFAULT 1,
  "multiplier" decimal(10,6) NOT NULL DEFAULT 1,
  "window_start" timestamptz NOT NULL,
  "window_end" timestamptz NOT NULL,
  "processed_at" timestamptz,
  "is_processed" boolean NOT NULL DEFAULT false,
  PRIMARY KEY ("id", "created_at"),
  UNIQUE ("idempotency_key", "created_at")
) PARTITION BY RANGE (created_at);

-- Create monthly partitions for activity_events
SELECT partman.create_parent(
  p_parent_table => 'public.activity_events',
  p_control => 'created_at',
  p_type => 'range',
  p_interval => 'monthly',
  p_premake => 3
);

-- =====================================================
-- Activity Campaigns Table
-- =====================================================
CREATE TABLE "public"."activity_campaigns" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "created_at" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "name" varchar(255) NOT NULL,
  "description" text,
  "status" varchar(20) NOT NULL DEFAULT 'DRAFT',
  "start_time" timestamptz,
  "end_time" timestamptz,
  "target_user_levels" integer[],
  "max_participants" integer,
  "total_budget_usd" decimal(38,18) NOT NULL DEFAULT 0,
  "used_budget_usd" decimal(38,18) NOT NULL DEFAULT 0,
  "daily_budget_usd" decimal(38,18) NOT NULL DEFAULT 0,
  "user_budget_usd" decimal(38,18) NOT NULL DEFAULT 0,
  "config" jsonb,
  PRIMARY KEY ("id")
);

-- =====================================================
-- Activity Tasks Table
-- =====================================================
CREATE TABLE "public"."activity_tasks" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "created_at" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "campaign_id" uuid NOT NULL,
  "name" varchar(255) NOT NULL,
  "description" text,
  "task_type" varchar(20) NOT NULL,
  "activity_types" varchar(20)[],
  "min_volume" decimal(38,18) NOT NULL DEFAULT 0,
  "min_quantity" bigint NOT NULL DEFAULT 1,
  "required_count" bigint NOT NULL DEFAULT 1,
  "required_volume" decimal(38,18) NOT NULL DEFAULT 0,
  "time_window_hours" integer NOT NULL DEFAULT 24,
  "reward_type" varchar(20) NOT NULL,
  "base_reward_usd" decimal(38,18) NOT NULL DEFAULT 0,
  "reward_percentage" decimal(10,6) NOT NULL DEFAULT 0,
  "max_reward_usd" decimal(38,18) NOT NULL DEFAULT 0,
  "bonus_multiplier" decimal(10,6) NOT NULL DEFAULT 1,
  "rules" jsonb,
  "config" jsonb,
  "is_active" boolean NOT NULL DEFAULT true,
  "priority" integer NOT NULL DEFAULT 0,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_activity_tasks_campaign" FOREIGN KEY ("campaign_id") REFERENCES "public"."activity_campaigns" ("id") ON DELETE CASCADE
);

-- =====================================================
-- Reward Ledger Table (Partitioned by time)
-- =====================================================
CREATE TABLE "public"."reward_ledger" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "created_at" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "idempotency_key" varchar(255) NOT NULL,
  "external_ref" varchar(255) NOT NULL,
  "user_id" uuid NOT NULL,
  "user_address" varchar(100) NOT NULL,
  "entry_type" varchar(20) NOT NULL,
  "status" varchar(20) NOT NULL,
  "amount_usd" decimal(38,18) NOT NULL,
  "amount_sol" decimal(38,18) NOT NULL,
  "sol_price" decimal(38,18) NOT NULL,
  "source_type" varchar(50) NOT NULL,
  "source_id" uuid,
  "activity_event_id" uuid,
  "processed_at" timestamptz,
  "settled_at" timestamptz,
  "expires_at" timestamptz,
  "description" text,
  "metadata" jsonb,
  PRIMARY KEY ("id", "created_at"),
  UNIQUE ("idempotency_key", "created_at")
) PARTITION BY RANGE (created_at);

-- Create monthly partitions for reward_ledger
SELECT partman.create_parent(
  p_parent_table => 'public.reward_ledger',
  p_control => 'created_at',
  p_type => 'range',
  p_interval => 'monthly',
  p_premake => 3
);

-- =====================================================
-- User Task Progress Table
-- =====================================================
CREATE TABLE "public"."user_task_progress" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "created_at" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "user_id" uuid NOT NULL,
  "task_id" uuid NOT NULL,
  "status" varchar(20) NOT NULL,
  "current_count" bigint NOT NULL DEFAULT 0,
  "current_volume" decimal(38,18) NOT NULL DEFAULT 0,
  "completion_count" bigint NOT NULL DEFAULT 0,
  "total_reward_usd" decimal(38,18) NOT NULL DEFAULT 0,
  "period_start" timestamptz NOT NULL,
  "period_end" timestamptz NOT NULL,
  "last_activity_at" timestamptz,
  "completed_at" timestamptz,
  "expires_at" timestamptz,
  "current_streak" integer NOT NULL DEFAULT 0,
  "best_streak" integer NOT NULL DEFAULT 0,
  "last_streak_at" timestamptz,
  "metadata" jsonb,
  PRIMARY KEY ("id"),
  UNIQUE ("user_id", "task_id", "period_start"),
  CONSTRAINT "fk_user_task_progress_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE,
  CONSTRAINT "fk_user_task_progress_task" FOREIGN KEY ("task_id") REFERENCES "public"."activity_tasks" ("id") ON DELETE CASCADE
);

-- =====================================================
-- Optimized Indexes
-- =====================================================

-- Activity Events Indexes
CREATE INDEX CONCURRENTLY "idx_activity_events_user_type_time" ON "public"."activity_events" ("user_id", "event_type", "created_at");
CREATE INDEX CONCURRENTLY "idx_activity_events_external_ref" ON "public"."activity_events" ("external_ref", "created_at");
CREATE INDEX CONCURRENTLY "idx_activity_events_processing" ON "public"."activity_events" ("is_processed", "created_at") WHERE "is_processed" = false;
CREATE INDEX CONCURRENTLY "idx_activity_events_window" ON "public"."activity_events" ("window_start", "window_end");
CREATE INDEX CONCURRENTLY "idx_activity_events_user_window" ON "public"."activity_events" ("user_id", "event_type", "window_start", "window_end");

-- Activity Campaigns Indexes
CREATE INDEX CONCURRENTLY "idx_activity_campaigns_status_time" ON "public"."activity_campaigns" ("status", "start_time", "end_time");
CREATE INDEX CONCURRENTLY "idx_activity_campaigns_active" ON "public"."activity_campaigns" ("status") WHERE "status" = 'ACTIVE';

-- Activity Tasks Indexes
CREATE INDEX CONCURRENTLY "idx_activity_tasks_campaign_active" ON "public"."activity_tasks" ("campaign_id", "is_active");
CREATE INDEX CONCURRENTLY "idx_activity_tasks_priority" ON "public"."activity_tasks" ("priority" DESC, "created_at");
CREATE INDEX CONCURRENTLY "idx_activity_tasks_activity_types" ON "public"."activity_tasks" USING GIN ("activity_types");

-- Reward Ledger Indexes
CREATE INDEX CONCURRENTLY "idx_reward_ledger_user_status_time" ON "public"."reward_ledger" ("user_id", "status", "created_at");
CREATE INDEX CONCURRENTLY "idx_reward_ledger_external_ref" ON "public"."reward_ledger" ("external_ref", "created_at");
CREATE INDEX CONCURRENTLY "idx_reward_ledger_pending" ON "public"."reward_ledger" ("status", "created_at") WHERE "status" = 'PENDING';
CREATE INDEX CONCURRENTLY "idx_reward_ledger_expiry" ON "public"."reward_ledger" ("expires_at") WHERE "expires_at" IS NOT NULL;
CREATE INDEX CONCURRENTLY "idx_reward_ledger_source" ON "public"."reward_ledger" ("source_type", "source_id", "created_at");

-- User Task Progress Indexes
CREATE INDEX CONCURRENTLY "idx_user_task_progress_user_status" ON "public"."user_task_progress" ("user_id", "status");
CREATE INDEX CONCURRENTLY "idx_user_task_progress_task_active" ON "public"."user_task_progress" ("task_id", "status") WHERE "status" = 'ACTIVE';
CREATE INDEX CONCURRENTLY "idx_user_task_progress_expiry" ON "public"."user_task_progress" ("expires_at") WHERE "expires_at" IS NOT NULL;
CREATE INDEX CONCURRENTLY "idx_user_task_progress_period" ON "public"."user_task_progress" ("period_start", "period_end");

-- =====================================================
-- Performance Optimization Functions
-- =====================================================

-- Function to clean up old partitions
CREATE OR REPLACE FUNCTION cleanup_old_partitions()
RETURNS void AS $$
BEGIN
  -- Clean up activity_events partitions older than 12 months
  PERFORM partman.drop_partition_time('public.activity_events', '12 months');
  
  -- Clean up reward_ledger partitions older than 24 months
  PERFORM partman.drop_partition_time('public.reward_ledger', '24 months');
END;
$$ LANGUAGE plpgsql;

-- Schedule cleanup job (requires pg_cron extension)
-- SELECT cron.schedule('cleanup-partitions', '0 2 1 * *', 'SELECT cleanup_old_partitions();');

-- =====================================================
-- Materialized Views for Analytics
-- =====================================================

-- Daily user activity summary
CREATE MATERIALIZED VIEW "public"."daily_user_activity_summary" AS
SELECT 
  DATE(created_at) as activity_date,
  user_id,
  event_type,
  COUNT(*) as event_count,
  SUM(volume) as total_volume,
  SUM(quantity) as total_quantity
FROM activity_events
WHERE created_at >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY DATE(created_at), user_id, event_type;

CREATE UNIQUE INDEX ON "public"."daily_user_activity_summary" (activity_date, user_id, event_type);

-- Refresh materialized view daily
-- SELECT cron.schedule('refresh-daily-summary', '0 1 * * *', 'REFRESH MATERIALIZED VIEW CONCURRENTLY daily_user_activity_summary;');
