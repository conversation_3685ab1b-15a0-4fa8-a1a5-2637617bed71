package examples

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity"
)

// ExampleActivitySystemUsage demonstrates how to use the new activity system
func ExampleActivitySystemUsage() {
	ctx := context.Background()
	manager := activity.NewActivityManager()

	// 1. Create a trading campaign
	campaign, err := manager.CreateCampaign(ctx, &activity.CreateCampaignRequest{
		Name:        "Trading Rewards Campaign",
		Description: "Earn rewards for trading activities",
		StartTime:   timePtr(time.Now()),
		EndTime:     timePtr(time.Now().Add(30 * 24 * time.Hour)), // 30 days
		TargetUserLevels: []uint{1, 2, 3}, // Target Lv1-Lv3 users
		TotalBudgetUSD:   decimal.NewFromFloat(10000), // $10,000 budget
		DailyBudgetUSD:   decimal.NewFromFloat(500),   // $500 per day
		UserBudgetUSD:    decimal.NewFromFloat(100),   // $100 per user
		Config: map[string]interface{}{
			"min_trade_volume": 100,
			"bonus_weekends":   true,
		},
	})
	if err != nil {
		panic(err)
	}

	// 2. Create trading tasks
	
	// Daily trading task
	_, err = manager.CreateTask(ctx, &activity.CreateTaskRequest{
		CampaignID:      campaign.ID,
		Name:            "Daily Trading",
		Description:     "Complete at least 5 trades per day",
		TaskType:        model.TaskTypeDaily,
		ActivityTypes:   []model.ActivityEventType{model.ActivityEventTrade},
		RequiredCount:   5,
		MinVolume:       decimal.NewFromFloat(100), // Min $100 per trade
		TimeWindowHours: 24,
		RewardType:      model.RewardTypeFixed,
		BaseRewardUSD:   decimal.NewFromFloat(10), // $10 reward
		Priority:        1,
	})
	if err != nil {
		panic(err)
	}

	// Volume-based trading task
	_, err = manager.CreateTask(ctx, &activity.CreateTaskRequest{
		CampaignID:       campaign.ID,
		Name:             "High Volume Trading",
		Description:      "Trade at least $1000 volume per day",
		TaskType:         model.TaskTypeDaily,
		ActivityTypes:    []model.ActivityEventType{model.ActivityEventTrade},
		RequiredVolume:   decimal.NewFromFloat(1000),
		TimeWindowHours:  24,
		RewardType:       model.RewardTypePercentage,
		RewardPercentage: decimal.NewFromFloat(0.001), // 0.1% of volume
		MaxRewardUSD:     decimal.NewFromFloat(50),     // Max $50 per day
		Priority:         2,
	})
	if err != nil {
		panic(err)
	}

	// 3. Create a check-in campaign
	checkinCampaign, err := manager.CreateCampaign(ctx, &activity.CreateCampaignRequest{
		Name:           "Daily Check-in Rewards",
		Description:    "Earn rewards for daily check-ins",
		StartTime:      timePtr(time.Now()),
		EndTime:        nil, // No end time
		TotalBudgetUSD: decimal.Zero, // Unlimited budget
		Config: map[string]interface{}{
			"streak_bonus": true,
		},
	})
	if err != nil {
		panic(err)
	}

	// Daily check-in task
	_, err = manager.CreateTask(ctx, &activity.CreateTaskRequest{
		CampaignID:      checkinCampaign.ID,
		Name:            "Daily Check-in",
		Description:     "Check in daily to earn rewards",
		TaskType:        model.TaskTypeDaily,
		ActivityTypes:   []model.ActivityEventType{model.ActivityEventCheckin},
		RequiredCount:   1,
		TimeWindowHours: 24,
		RewardType:      model.RewardTypeProgressive,
		BaseRewardUSD:   decimal.NewFromFloat(1), // Base $1 reward
		BonusMultiplier: decimal.NewFromFloat(1.1), // 10% bonus for streaks
		MaxRewardUSD:    decimal.NewFromFloat(10),  // Max $10 for long streaks
		Priority:        1,
	})
	if err != nil {
		panic(err)
	}

	// 4. Create social activity campaign
	socialCampaign, err := manager.CreateCampaign(ctx, &activity.CreateCampaignRequest{
		Name:           "Social Engagement Rewards",
		Description:    "Earn rewards for social activities",
		StartTime:      timePtr(time.Now()),
		EndTime:        timePtr(time.Now().Add(7 * 24 * time.Hour)), // 7 days
		TotalBudgetUSD: decimal.NewFromFloat(1000),
		DailyBudgetUSD: decimal.NewFromFloat(200),
		Config: map[string]interface{}{
			"platforms": []string{"twitter", "telegram", "discord"},
		},
	})
	if err != nil {
		panic(err)
	}

	// Social activity tasks
	socialActivities := []struct {
		name         string
		activityType model.ActivityEventType
		reward       float64
		maxDaily     int64
	}{
		{"Like Posts", model.ActivityEventLike, 0.1, 50},
		{"Share Posts", model.ActivityEventShare, 0.5, 20},
		{"Create Posts", model.ActivityEventPost, 2.0, 5},
	}

	for _, social := range socialActivities {
		_, err = manager.CreateTask(ctx, &activity.CreateTaskRequest{
			CampaignID:      socialCampaign.ID,
			Name:            social.name,
			Description:     fmt.Sprintf("Earn rewards for %s", social.name),
			TaskType:        model.TaskTypeDaily,
			ActivityTypes:   []model.ActivityEventType{social.activityType},
			RequiredCount:   1,
			TimeWindowHours: 24,
			RewardType:      model.RewardTypeFixed,
			BaseRewardUSD:   decimal.NewFromFloat(social.reward),
			Priority:        1,
			Config: map[string]interface{}{
				"max_daily_completions": social.maxDaily,
			},
		})
		if err != nil {
			panic(err)
		}
	}

	// 5. Simulate user activities
	userID := uuid.New()
	userAddress := "<EMAIL>"

	// Simulate trading activity
	err = manager.CreateTradeEvent(ctx, &activity.TradeEventRequest{
		OrderID:     uuid.New().String(),
		UserID:      userID,
		UserAddress: userAddress,
		BaseSymbol:  "SOL",
		QuoteSymbol: "USDC",
		OrderType:   "BUY",
		Volume:      decimal.NewFromFloat(500), // $500 trade
	})
	if err != nil {
		panic(err)
	}

	// Simulate check-in activity
	err = manager.CreateCheckinEvent(ctx, &activity.CheckinEventRequest{
		UserID:           userID,
		UserAddress:      userAddress,
		CheckinType:      "daily",
		StreakDay:        5, // 5-day streak
		StreakMultiplier: decimal.NewFromFloat(1.5), // 50% bonus
	})
	if err != nil {
		panic(err)
	}

	// Simulate social activities
	socialEvents := []struct {
		socialType string
		contentID  string
		quantity   int64
	}{
		{"like", "post_123", 1},
		{"share", "post_123", 1},
		{"post", "my_post_456", 1},
	}

	for _, social := range socialEvents {
		err = manager.CreateSocialEvent(ctx, &activity.SocialEventRequest{
			UserID:      userID,
			UserAddress: userAddress,
			SocialType:  social.socialType,
			ContentID:   social.contentID,
			Platform:    "twitter",
			Quantity:    social.quantity,
		})
		if err != nil {
			panic(err)
		}
	}

	// 6. Check user progress
	progress, err := manager.GetUserProgress(ctx, userID)
	if err != nil {
		panic(err)
	}

	fmt.Printf("User has %d active tasks in progress\n", len(progress))
	for _, p := range progress {
		fmt.Printf("Task: %s, Progress: %d/%d, Volume: %s\n",
			p.Task.Name,
			p.CurrentCount,
			p.Task.RequiredCount,
			p.CurrentVolume.String())
	}
}

// Helper function
func timePtr(t time.Time) *time.Time {
	return &t
}

// Example of creating a custom campaign with complex rules
func ExampleComplexCampaign() {
	ctx := context.Background()
	manager := activity.NewActivityManager()

	// Create a tiered trading campaign
	campaign, err := manager.CreateCampaign(ctx, &activity.CreateCampaignRequest{
		Name:        "Tiered Trading Championship",
		Description: "Multi-tier trading competition with increasing rewards",
		StartTime:   timePtr(time.Now()),
		EndTime:     timePtr(time.Now().Add(14 * 24 * time.Hour)), // 2 weeks
		TargetUserLevels: []uint{3, 4, 5, 6}, // High-level users only
		TotalBudgetUSD:   decimal.NewFromFloat(50000),
		Config: map[string]interface{}{
			"competition_type": "tiered",
			"tiers": map[string]interface{}{
				"bronze": map[string]interface{}{
					"min_volume": 1000,
					"max_volume": 10000,
					"reward_rate": 0.002, // 0.2%
				},
				"silver": map[string]interface{}{
					"min_volume": 10000,
					"max_volume": 50000,
					"reward_rate": 0.003, // 0.3%
				},
				"gold": map[string]interface{}{
					"min_volume": 50000,
					"reward_rate": 0.005, // 0.5%
				},
			},
		},
	})
	if err != nil {
		panic(err)
	}

	// Create tiered task
	_, err = manager.CreateTask(ctx, &activity.CreateTaskRequest{
		CampaignID:      campaign.ID,
		Name:            "Tiered Trading Rewards",
		Description:     "Earn increasing rewards based on trading volume tiers",
		TaskType:        model.TaskTypeCumulative,
		ActivityTypes:   []model.ActivityEventType{model.ActivityEventTrade},
		TimeWindowHours: 24 * 14, // 2 weeks
		RewardType:      model.RewardTypeTiered,
		Rules: map[string]interface{}{
			"tiers": []map[string]interface{}{
				{
					"name":        "Bronze",
					"min_volume":  1000,
					"max_volume":  10000,
					"reward_rate": 0.002,
				},
				{
					"name":        "Silver", 
					"min_volume":  10000,
					"max_volume":  50000,
					"reward_rate": 0.003,
				},
				{
					"name":        "Gold",
					"min_volume":  50000,
					"reward_rate": 0.005,
				},
			},
		},
		Priority: 1,
	})
	if err != nil {
		panic(err)
	}
}
