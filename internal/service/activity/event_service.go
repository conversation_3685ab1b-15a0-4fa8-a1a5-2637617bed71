package activity

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// EventService handles activity event processing with deduplication
type EventService struct {
	db *gorm.DB
}

// NewEventService creates a new event service
func NewEventService() *EventService {
	return &EventService{
		db: global.GVA_DB,
	}
}

// CreateActivityEvent creates a new activity event with deduplication
func (s *EventService) CreateActivityEvent(ctx context.Context, req *CreateEventRequest) (*model.ActivityEvent, error) {
	// Generate idempotency key if not provided
	if req.IdempotencyKey == "" {
		req.IdempotencyKey = s.generateIdempotencyKey(req)
	}

	// Check for existing event with same idempotency key
	existingEvent, err := s.getEventByIdempotencyKey(ctx, req.IdempotencyKey)
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to check existing event: %w", err)
	}

	if existingEvent != nil {
		global.GVA_LOG.Debug("Event already exists, returning existing event",
			zap.String("idempotency_key", req.IdempotencyKey),
			zap.String("event_id", existingEvent.ID.String()))
		return existingEvent, nil
	}

	// Check for duplicate events in time window
	if err := s.checkTimeWindowDuplication(ctx, req); err != nil {
		return nil, fmt.Errorf("duplicate event in time window: %w", err)
	}

	// Create new event
	event := &model.ActivityEvent{
		EventType:      req.EventType,
		ExternalRef:    req.ExternalRef,
		IdempotencyKey: req.IdempotencyKey,
		UserID:         req.UserID,
		UserAddress:    req.UserAddress,
		EventData:      req.EventData,
		Volume:         req.Volume,
		Quantity:       req.Quantity,
		Multiplier:     req.Multiplier,
		WindowStart:    req.WindowStart,
		WindowEnd:      req.WindowEnd,
		IsProcessed:    false,
	}

	if err := s.db.WithContext(ctx).Create(event).Error; err != nil {
		return nil, fmt.Errorf("failed to create activity event: %w", err)
	}

	global.GVA_LOG.Info("Activity event created successfully",
		zap.String("event_id", event.ID.String()),
		zap.String("event_type", string(event.EventType)),
		zap.String("user_id", event.UserID.String()),
		zap.String("external_ref", event.ExternalRef))

	return event, nil
}

// ProcessPendingEvents processes all pending events
func (s *EventService) ProcessPendingEvents(ctx context.Context) error {
	var events []model.ActivityEvent
	
	err := s.db.WithContext(ctx).
		Where("is_processed = ? AND created_at > ?", false, time.Now().Add(-24*time.Hour)).
		Order("created_at ASC").
		Limit(1000). // Process in batches
		Find(&events).Error
	
	if err != nil {
		return fmt.Errorf("failed to fetch pending events: %w", err)
	}

	global.GVA_LOG.Info("Processing pending events", zap.Int("count", len(events)))

	for _, event := range events {
		if err := s.processEvent(ctx, &event); err != nil {
			global.GVA_LOG.Error("Failed to process event",
				zap.String("event_id", event.ID.String()),
				zap.Error(err))
			continue
		}
	}

	return nil
}

// processEvent processes a single event
func (s *EventService) processEvent(ctx context.Context, event *model.ActivityEvent) error {
	// Start transaction
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Mark event as processed
	now := time.Now()
	event.ProcessedAt = &now
	event.IsProcessed = true

	if err := tx.Save(event).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update event status: %w", err)
	}

	// Process rewards for this event
	if err := s.processEventRewards(ctx, tx, event); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to process event rewards: %w", err)
	}

	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	global.GVA_LOG.Debug("Event processed successfully",
		zap.String("event_id", event.ID.String()))

	return nil
}

// processEventRewards processes rewards for an event
func (s *EventService) processEventRewards(ctx context.Context, tx *gorm.DB, event *model.ActivityEvent) error {
	// This will be implemented in the reward service
	// For now, just log the event
	global.GVA_LOG.Debug("Processing event rewards",
		zap.String("event_id", event.ID.String()),
		zap.String("event_type", string(event.EventType)),
		zap.String("volume", event.Volume.String()))

	return nil
}

// generateIdempotencyKey generates a unique idempotency key
func (s *EventService) generateIdempotencyKey(req *CreateEventRequest) string {
	return fmt.Sprintf("%s_%s_%s_%d_%d",
		req.UserID.String(),
		req.EventType,
		req.ExternalRef,
		req.WindowStart.Unix(),
		req.WindowEnd.Unix())
}

// getEventByIdempotencyKey retrieves an event by idempotency key
func (s *EventService) getEventByIdempotencyKey(ctx context.Context, key string) (*model.ActivityEvent, error) {
	var event model.ActivityEvent
	err := s.db.WithContext(ctx).Where("idempotency_key = ?", key).First(&event).Error
	if err != nil {
		return nil, err
	}
	return &event, nil
}

// checkTimeWindowDuplication checks for duplicate events in time window
func (s *EventService) checkTimeWindowDuplication(ctx context.Context, req *CreateEventRequest) error {
	var count int64
	
	err := s.db.WithContext(ctx).Model(&model.ActivityEvent{}).
		Where("user_id = ? AND event_type = ? AND external_ref = ?", req.UserID, req.EventType, req.ExternalRef).
		Where("window_start <= ? AND window_end >= ?", req.WindowEnd, req.WindowStart).
		Count(&count).Error
	
	if err != nil {
		return fmt.Errorf("failed to check time window duplication: %w", err)
	}

	if count > 0 {
		return fmt.Errorf("duplicate event found in time window")
	}

	return nil
}

// CreateEventRequest represents a request to create an activity event
type CreateEventRequest struct {
	EventType      model.ActivityEventType    `json:"event_type"`
	ExternalRef    string                     `json:"external_ref"`
	IdempotencyKey string                     `json:"idempotency_key,omitempty"`
	UserID         uuid.UUID                  `json:"user_id"`
	UserAddress    string                     `json:"user_address"`
	EventData      map[string]interface{}     `json:"event_data,omitempty"`
	Volume         decimal.Decimal            `json:"volume"`
	Quantity       int64                      `json:"quantity"`
	Multiplier     decimal.Decimal            `json:"multiplier"`
	WindowStart    time.Time                  `json:"window_start"`
	WindowEnd      time.Time                  `json:"window_end"`
}
