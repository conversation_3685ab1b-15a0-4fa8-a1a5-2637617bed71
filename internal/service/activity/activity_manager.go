package activity

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// ActivityManager is the main service for managing the activity system
type ActivityManager struct {
	db            *gorm.DB
	eventService  *EventService
	rewardService *RewardService
}

// NewActivityManager creates a new activity manager
func NewActivityManager() *ActivityManager {
	return &ActivityManager{
		db:            global.GVA_DB,
		eventService:  NewEventService(),
		rewardService: NewRewardService(),
	}
}

// CreateTradeEvent creates a trade activity event
func (am *ActivityManager) CreateTradeEvent(ctx context.Context, req *TradeEventRequest) error {
	windowStart := time.Now().Truncate(time.Hour)
	windowEnd := windowStart.Add(time.Hour)

	eventReq := &CreateEventRequest{
		EventType:   model.ActivityEventTrade,
		ExternalRef: req.OrderID,
		UserID:      req.UserID,
		UserAddress: req.UserAddress,
		EventData: map[string]interface{}{
			"order_id":     req.OrderID,
			"base_symbol":  req.BaseSymbol,
			"quote_symbol": req.QuoteSymbol,
			"order_type":   req.OrderType,
		},
		Volume:      req.Volume,
		Quantity:    1,
		Multiplier:  decimal.NewFromFloat(1.0),
		WindowStart: windowStart,
		WindowEnd:   windowEnd,
	}

	event, err := am.eventService.CreateActivityEvent(ctx, eventReq)
	if err != nil {
		return fmt.Errorf("failed to create trade event: %w", err)
	}

	return am.rewardService.ProcessEventRewards(ctx, event)
}

// CreateCheckinEvent creates a check-in activity event
func (am *ActivityManager) CreateCheckinEvent(ctx context.Context, req *CheckinEventRequest) error {
	// Check-in events use daily windows
	now := time.Now()
	windowStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	windowEnd := windowStart.Add(24 * time.Hour)

	eventReq := &CreateEventRequest{
		EventType:   model.ActivityEventCheckin,
		ExternalRef: fmt.Sprintf("checkin_%s_%s", req.UserID.String(), windowStart.Format("2006-01-02")),
		UserID:      req.UserID,
		UserAddress: req.UserAddress,
		EventData: map[string]interface{}{
			"checkin_type": req.CheckinType,
			"streak_day":   req.StreakDay,
		},
		Volume:      decimal.Zero,
		Quantity:    1,
		Multiplier:  req.StreakMultiplier,
		WindowStart: windowStart,
		WindowEnd:   windowEnd,
	}

	event, err := am.eventService.CreateActivityEvent(ctx, eventReq)
	if err != nil {
		return fmt.Errorf("failed to create checkin event: %w", err)
	}

	return am.rewardService.ProcessEventRewards(ctx, event)
}

// CreateSocialEvent creates a social activity event (like, share, post)
func (am *ActivityManager) CreateSocialEvent(ctx context.Context, req *SocialEventRequest) error {
	// Social events use hourly windows to prevent spam
	windowStart := time.Now().Truncate(time.Hour)
	windowEnd := windowStart.Add(time.Hour)

	var eventType model.ActivityEventType
	switch req.SocialType {
	case "like":
		eventType = model.ActivityEventLike
	case "share":
		eventType = model.ActivityEventShare
	case "post":
		eventType = model.ActivityEventPost
	default:
		return fmt.Errorf("unsupported social type: %s", req.SocialType)
	}

	eventReq := &CreateEventRequest{
		EventType:   eventType,
		ExternalRef: req.ContentID,
		UserID:      req.UserID,
		UserAddress: req.UserAddress,
		EventData: map[string]interface{}{
			"social_type": req.SocialType,
			"content_id":  req.ContentID,
			"platform":    req.Platform,
		},
		Volume:      decimal.Zero,
		Quantity:    req.Quantity,
		Multiplier:  decimal.NewFromFloat(1.0),
		WindowStart: windowStart,
		WindowEnd:   windowEnd,
	}

	event, err := am.eventService.CreateActivityEvent(ctx, eventReq)
	if err != nil {
		return fmt.Errorf("failed to create social event: %w", err)
	}

	return am.rewardService.ProcessEventRewards(ctx, event)
}

// CreateCampaign creates a new activity campaign
func (am *ActivityManager) CreateCampaign(ctx context.Context, req *CreateCampaignRequest) (*model.ActivityCampaign, error) {
	campaign := &model.ActivityCampaign{
		Name:             req.Name,
		Description:      req.Description,
		Status:           model.CampaignStatusDraft,
		StartTime:        req.StartTime,
		EndTime:          req.EndTime,
		TargetUserLevels: req.TargetUserLevels,
		MaxParticipants:  req.MaxParticipants,
		TotalBudgetUSD:   req.TotalBudgetUSD,
		DailyBudgetUSD:   req.DailyBudgetUSD,
		UserBudgetUSD:    req.UserBudgetUSD,
		Config:           req.Config,
	}

	if err := am.db.WithContext(ctx).Create(campaign).Error; err != nil {
		return nil, fmt.Errorf("failed to create campaign: %w", err)
	}

	global.GVA_LOG.Info("Campaign created successfully",
		zap.String("campaign_id", campaign.ID.String()),
		zap.String("name", campaign.Name))

	return campaign, nil
}

// CreateTask creates a new activity task
func (am *ActivityManager) CreateTask(ctx context.Context, req *CreateTaskRequest) (*model.ActivityTask, error) {
	task := &model.ActivityTask{
		CampaignID:        req.CampaignID,
		Name:              req.Name,
		Description:       req.Description,
		TaskType:          req.TaskType,
		ActivityTypes:     req.ActivityTypes,
		MinVolume:         req.MinVolume,
		MinQuantity:       req.MinQuantity,
		RequiredCount:     req.RequiredCount,
		RequiredVolume:    req.RequiredVolume,
		TimeWindowHours:   req.TimeWindowHours,
		RewardType:        req.RewardType,
		BaseRewardUSD:     req.BaseRewardUSD,
		RewardPercentage:  req.RewardPercentage,
		MaxRewardUSD:      req.MaxRewardUSD,
		BonusMultiplier:   req.BonusMultiplier,
		Rules:             req.Rules,
		Config:            req.Config,
		Priority:          req.Priority,
	}

	if err := am.db.WithContext(ctx).Create(task).Error; err != nil {
		return nil, fmt.Errorf("failed to create task: %w", err)
	}

	global.GVA_LOG.Info("Task created successfully",
		zap.String("task_id", task.ID.String()),
		zap.String("name", task.Name),
		zap.String("campaign_id", task.CampaignID.String()))

	return task, nil
}

// GetUserProgress gets user progress for all active tasks
func (am *ActivityManager) GetUserProgress(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error) {
	var progress []model.UserTaskProgress
	err := am.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Campaign").
		Where("user_id = ? AND status = ?", userID, model.ProgressStatusActive).
		Order("updated_at DESC").
		Find(&progress).Error

	return progress, err
}

// ProcessExpiredEntries processes expired ledger entries and task progress
func (am *ActivityManager) ProcessExpiredEntries(ctx context.Context) error {
	now := time.Now()

	// Expire pending ledger entries
	result := am.db.WithContext(ctx).
		Model(&model.RewardLedger{}).
		Where("status = ? AND expires_at < ?", model.LedgerStatusPending, now).
		Update("status", model.LedgerStatusExpired)

	if result.Error != nil {
		return fmt.Errorf("failed to expire ledger entries: %w", result.Error)
	}

	global.GVA_LOG.Info("Expired ledger entries processed",
		zap.Int64("count", result.RowsAffected))

	// Expire task progress
	result = am.db.WithContext(ctx).
		Model(&model.UserTaskProgress{}).
		Where("status = ? AND expires_at < ?", model.ProgressStatusActive, now).
		Update("status", model.ProgressStatusExpired)

	if result.Error != nil {
		return fmt.Errorf("failed to expire task progress: %w", result.Error)
	}

	global.GVA_LOG.Info("Expired task progress processed",
		zap.Int64("count", result.RowsAffected))

	return nil
}

// Request types
type TradeEventRequest struct {
	OrderID     string          `json:"order_id"`
	UserID      uuid.UUID       `json:"user_id"`
	UserAddress string          `json:"user_address"`
	BaseSymbol  string          `json:"base_symbol"`
	QuoteSymbol string          `json:"quote_symbol"`
	OrderType   string          `json:"order_type"`
	Volume      decimal.Decimal `json:"volume"`
}

type CheckinEventRequest struct {
	UserID          uuid.UUID       `json:"user_id"`
	UserAddress     string          `json:"user_address"`
	CheckinType     string          `json:"checkin_type"`
	StreakDay       int             `json:"streak_day"`
	StreakMultiplier decimal.Decimal `json:"streak_multiplier"`
}

type SocialEventRequest struct {
	UserID      uuid.UUID `json:"user_id"`
	UserAddress string    `json:"user_address"`
	SocialType  string    `json:"social_type"` // like, share, post
	ContentID   string    `json:"content_id"`
	Platform    string    `json:"platform"`
	Quantity    int64     `json:"quantity"`
}

type CreateCampaignRequest struct {
	Name             string                     `json:"name"`
	Description      string                     `json:"description"`
	StartTime        *time.Time                 `json:"start_time"`
	EndTime          *time.Time                 `json:"end_time"`
	TargetUserLevels []uint                     `json:"target_user_levels"`
	MaxParticipants  *int                       `json:"max_participants"`
	TotalBudgetUSD   decimal.Decimal            `json:"total_budget_usd"`
	DailyBudgetUSD   decimal.Decimal            `json:"daily_budget_usd"`
	UserBudgetUSD    decimal.Decimal            `json:"user_budget_usd"`
	Config           map[string]interface{}     `json:"config"`
}

type CreateTaskRequest struct {
	CampaignID       uuid.UUID                  `json:"campaign_id"`
	Name             string                     `json:"name"`
	Description      string                     `json:"description"`
	TaskType         model.TaskType             `json:"task_type"`
	ActivityTypes    []model.ActivityEventType  `json:"activity_types"`
	MinVolume        decimal.Decimal            `json:"min_volume"`
	MinQuantity      int64                      `json:"min_quantity"`
	RequiredCount    int64                      `json:"required_count"`
	RequiredVolume   decimal.Decimal            `json:"required_volume"`
	TimeWindowHours  int                        `json:"time_window_hours"`
	RewardType       model.RewardType           `json:"reward_type"`
	BaseRewardUSD    decimal.Decimal            `json:"base_reward_usd"`
	RewardPercentage decimal.Decimal            `json:"reward_percentage"`
	MaxRewardUSD     decimal.Decimal            `json:"max_reward_usd"`
	BonusMultiplier  decimal.Decimal            `json:"bonus_multiplier"`
	Rules            map[string]interface{}     `json:"rules"`
	Config           map[string]interface{}     `json:"config"`
	Priority         int                        `json:"priority"`
}
