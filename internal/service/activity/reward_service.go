package activity

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// RewardService handles reward processing and ledger management
type RewardService struct {
	db           *gorm.DB
	eventService *EventService
}

// NewRewardService creates a new reward service
func NewRewardService() *RewardService {
	return &RewardService{
		db:           global.GVA_DB,
		eventService: NewEventService(),
	}
}

// ProcessEventRewards processes rewards for an activity event
func (s *RewardService) ProcessEventRewards(ctx context.Context, event *model.ActivityEvent) error {
	// Get user with level information
	user, err := s.getUserWithLevel(ctx, event.UserID)
	if err != nil {
		return fmt.Errorf("failed to get user with level: %w", err)
	}

	// Get active campaigns for this user
	campaigns, err := s.getActiveCampaignsForUser(ctx, user)
	if err != nil {
		return fmt.Errorf("failed to get active campaigns: %w", err)
	}

	global.GVA_LOG.Debug("Processing event rewards",
		zap.String("event_id", event.ID.String()),
		zap.String("user_id", event.UserID.String()),
		zap.Int("active_campaigns", len(campaigns)))

	// Process each campaign
	for _, campaign := range campaigns {
		if err := s.processCampaignRewards(ctx, event, user, &campaign); err != nil {
			global.GVA_LOG.Error("Failed to process campaign rewards",
				zap.String("campaign_id", campaign.ID.String()),
				zap.Error(err))
			continue
		}
	}

	return nil
}

// processCampaignRewards processes rewards for a specific campaign
func (s *RewardService) processCampaignRewards(ctx context.Context, event *model.ActivityEvent, user *model.User, campaign *model.ActivityCampaign) error {
	// Get matching tasks for this event
	tasks, err := s.getMatchingTasks(ctx, campaign.ID, event)
	if err != nil {
		return fmt.Errorf("failed to get matching tasks: %w", err)
	}

	for _, task := range tasks {
		if err := s.processTaskReward(ctx, event, user, campaign, &task); err != nil {
			global.GVA_LOG.Error("Failed to process task reward",
				zap.String("task_id", task.ID.String()),
				zap.Error(err))
			continue
		}
	}

	return nil
}

// processTaskReward processes reward for a specific task
func (s *RewardService) processTaskReward(ctx context.Context, event *model.ActivityEvent, user *model.User, campaign *model.ActivityCampaign, task *model.ActivityTask) error {
	// Start transaction
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Get or create user task progress
	progress, err := s.getOrCreateTaskProgress(ctx, tx, user.ID, task.ID)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// Add progress
	progress.AddProgress(event.Volume, event.Quantity)

	// Check if task can be completed
	if progress.CanComplete(task) {
		// Calculate reward
		reward := task.CalculateReward(progress.CurrentVolume, progress.CurrentCount, event.Multiplier)

		// Check campaign budget
		if !s.checkCampaignBudget(campaign, reward) {
			tx.Rollback()
			return fmt.Errorf("insufficient campaign budget")
		}

		// Create ledger entry
		if err := s.createRewardLedgerEntry(ctx, tx, &CreateLedgerRequest{
			UserID:          user.ID,
			UserAddress:     user.Email, // Assuming email as address for now
			AmountUSD:       reward,
			SourceType:      "TASK",
			SourceID:        task.ID,
			ActivityEventID: &event.ID,
			Description:     fmt.Sprintf("Task completion reward: %s", task.Name),
		}); err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to create ledger entry: %w", err)
		}

		// Mark task as completed
		progress.Complete(reward)

		// Update campaign budget
		campaign.UsedBudgetUSD = campaign.UsedBudgetUSD.Add(reward)
		if err := tx.Save(campaign).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to update campaign budget: %w", err)
		}

		global.GVA_LOG.Info("Task completed and reward created",
			zap.String("task_id", task.ID.String()),
			zap.String("user_id", user.ID.String()),
			zap.String("reward_usd", reward.String()))
	}

	// Save progress
	if err := tx.Save(progress).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to save task progress: %w", err)
	}

	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// createRewardLedgerEntry creates a new reward ledger entry
func (s *RewardService) createRewardLedgerEntry(ctx context.Context, tx *gorm.DB, req *CreateLedgerRequest) error {
	// Get current SOL price (simplified - should get from price service)
	solPrice := decimal.NewFromFloat(100.0) // Placeholder

	// Calculate SOL amount
	amountSOL := req.AmountUSD.Div(solPrice)

	// Generate idempotency key
	idempotencyKey := fmt.Sprintf("reward_%s_%s_%d",
		req.UserID.String(),
		req.SourceID.String(),
		time.Now().Unix())

	// Check for existing entry
	var existingCount int64
	if err := tx.Model(&model.RewardLedger{}).
		Where("idempotency_key = ?", idempotencyKey).
		Count(&existingCount).Error; err != nil {
		return fmt.Errorf("failed to check existing ledger entry: %w", err)
	}

	if existingCount > 0 {
		return nil // Entry already exists
	}

	// Create ledger entry
	entry := &model.RewardLedger{
		IdempotencyKey:  idempotencyKey,
		ExternalRef:     req.ExternalRef,
		UserID:          req.UserID,
		UserAddress:     req.UserAddress,
		EntryType:       model.LedgerEntryCredit,
		Status:          model.LedgerStatusPending,
		AmountUSD:       req.AmountUSD,
		AmountSOL:       amountSOL,
		SolPrice:        solPrice,
		SourceType:      req.SourceType,
		SourceID:        req.SourceID,
		ActivityEventID: req.ActivityEventID,
		Description:     req.Description,
		Metadata:        req.Metadata,
	}

	// Set expiration (30 days from now)
	expiresAt := time.Now().Add(30 * 24 * time.Hour)
	entry.ExpiresAt = &expiresAt

	if err := tx.Create(entry).Error; err != nil {
		return fmt.Errorf("failed to create ledger entry: %w", err)
	}

	return nil
}

// Helper functions
func (s *RewardService) getUserWithLevel(ctx context.Context, userID uuid.UUID) (*model.User, error) {
	var user model.User
	err := s.db.WithContext(ctx).
		Preload("AgentLevel").
		Where("id = ?", userID).
		First(&user).Error
	return &user, err
}

func (s *RewardService) getActiveCampaignsForUser(ctx context.Context, user *model.User) ([]model.ActivityCampaign, error) {
	var campaigns []model.ActivityCampaign
	query := s.db.WithContext(ctx).
		Where("status = ?", model.CampaignStatusActive).
		Where("(start_time IS NULL OR start_time <= ?) AND (end_time IS NULL OR end_time >= ?)", time.Now(), time.Now())

	// Filter by user level if specified
	query = query.Where("target_user_levels IS NULL OR ? = ANY(target_user_levels)", user.AgentLevelID)

	err := query.Find(&campaigns).Error
	return campaigns, err
}

func (s *RewardService) getMatchingTasks(ctx context.Context, campaignID uuid.UUID, event *model.ActivityEvent) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := s.db.WithContext(ctx).
		Where("campaign_id = ? AND is_active = ?", campaignID, true).
		Where("activity_types IS NULL OR ? = ANY(activity_types)", event.EventType).
		Order("priority DESC").
		Find(&tasks).Error
	return tasks, err
}

func (s *RewardService) getOrCreateTaskProgress(ctx context.Context, tx *gorm.DB, userID, taskID uuid.UUID) (*model.UserTaskProgress, error) {
	var progress model.UserTaskProgress
	
	// Try to find existing active progress
	err := tx.Where("user_id = ? AND task_id = ? AND status = ?", userID, taskID, model.ProgressStatusActive).
		First(&progress).Error
	
	if err == gorm.ErrRecordNotFound {
		// Create new progress
		now := time.Now()
		progress = model.UserTaskProgress{
			UserID:      userID,
			TaskID:      taskID,
			Status:      model.ProgressStatusActive,
			PeriodStart: now,
			PeriodEnd:   now.Add(24 * time.Hour), // Default 24 hour period
		}
		
		if err := tx.Create(&progress).Error; err != nil {
			return nil, err
		}
	} else if err != nil {
		return nil, err
	}
	
	return &progress, nil
}

func (s *RewardService) checkCampaignBudget(campaign *model.ActivityCampaign, rewardAmount decimal.Decimal) bool {
	if campaign.TotalBudgetUSD.IsZero() {
		return true // Unlimited budget
	}
	return campaign.UsedBudgetUSD.Add(rewardAmount).LessThanOrEqual(campaign.TotalBudgetUSD)
}

// CreateLedgerRequest represents a request to create a ledger entry
type CreateLedgerRequest struct {
	UserID          uuid.UUID                  `json:"user_id"`
	UserAddress     string                     `json:"user_address"`
	AmountUSD       decimal.Decimal            `json:"amount_usd"`
	ExternalRef     string                     `json:"external_ref,omitempty"`
	SourceType      string                     `json:"source_type"`
	SourceID        uuid.UUID                  `json:"source_id"`
	ActivityEventID *uuid.UUID                 `json:"activity_event_id,omitempty"`
	Description     string                     `json:"description"`
	Metadata        map[string]interface{}     `json:"metadata,omitempty"`
}
