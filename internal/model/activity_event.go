package model

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// ActivityEventType defines the type of activity event
type ActivityEventType string

const (
	ActivityEventTrade   ActivityEventType = "TRADE"
	ActivityEventCheckin ActivityEventType = "CHECKIN"
	ActivityEventLike    ActivityEventType = "LIKE"
	ActivityEventShare   ActivityEventType = "SHARE"
	ActivityEventPost    ActivityEventType = "POST"
	ActivityEventRefer   ActivityEventType = "REFER"
	ActivityEventCustom  ActivityEventType = "CUSTOM"
)

// ActivityEvent represents an immutable event record for all user activities
// This is the source of truth for all activity-based rewards
type ActivityEvent struct {
	ID        uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	CreatedAt time.Time `gorm:"index;not null" json:"created_at"`

	// Event identification and deduplication
	EventType     ActivityEventType `gorm:"type:varchar(20);not null;index" json:"event_type"`
	ExternalRef   string            `gorm:"type:varchar(255);not null;index" json:"external_ref"` // External reference (tx_hash, post_id, etc.)
	IdempotencyKey string           `gorm:"type:varchar(255);not null;unique" json:"idempotency_key"`

	// User information
	UserID      uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`
	UserAddress string    `gorm:"type:varchar(100);not null;index" json:"user_address"`

	// Event data (flexible JSON for different event types)
	EventData map[string]interface{} `gorm:"type:jsonb" json:"event_data"`

	// Quantifiable metrics
	Volume    decimal.Decimal `gorm:"type:decimal(38,18);default:0" json:"volume"`     // Trading volume, post views, etc.
	Quantity  int64           `gorm:"default:1" json:"quantity"`                       // Number of actions (likes, shares, etc.)
	Multiplier decimal.Decimal `gorm:"type:decimal(10,6);default:1" json:"multiplier"` // Event-specific multiplier

	// Time window for deduplication
	WindowStart time.Time `gorm:"index" json:"window_start"`
	WindowEnd   time.Time `gorm:"index" json:"window_end"`

	// Processing status
	ProcessedAt *time.Time `gorm:"index" json:"processed_at"`
	IsProcessed bool       `gorm:"default:false;index" json:"is_processed"`

	// Relationships
	User User `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
}

// TableName specifies the table name for ActivityEvent
func (ActivityEvent) TableName() string {
	return "activity_events"
}

// GetIdempotencyKey generates a unique idempotency key for the event
func (ae *ActivityEvent) GetIdempotencyKey() string {
	if ae.IdempotencyKey != "" {
		return ae.IdempotencyKey
	}
	
	// Generate based on user, event type, external ref, and time window
	return fmt.Sprintf("%s_%s_%s_%d_%d", 
		ae.UserID.String(), 
		ae.EventType, 
		ae.ExternalRef,
		ae.WindowStart.Unix(),
		ae.WindowEnd.Unix())
}

// IsInTimeWindow checks if the event falls within a specific time window
func (ae *ActivityEvent) IsInTimeWindow(start, end time.Time) bool {
	return ae.CreatedAt.After(start) && ae.CreatedAt.Before(end)
}
