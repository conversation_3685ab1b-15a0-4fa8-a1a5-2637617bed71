package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// CampaignStatus defines the status of a campaign
type CampaignStatus string

const (
	CampaignStatusDraft    CampaignStatus = "DRAFT"
	CampaignStatusActive   CampaignStatus = "ACTIVE"
	CampaignStatusPaused   CampaignStatus = "PAUSED"
	CampaignStatusExpired  CampaignStatus = "EXPIRED"
	CampaignStatusArchived CampaignStatus = "ARCHIVED"
)

// ActivityCampaign represents a configurable campaign for activity rewards
type ActivityCampaign struct {
	ID        uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	CreatedAt time.Time `gorm:"index;not null" json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Campaign basic info
	Name        string         `gorm:"type:varchar(255);not null" json:"name"`
	Description string         `gorm:"type:text" json:"description"`
	Status      CampaignStatus `gorm:"type:varchar(20);not null;default:'DRAFT';index" json:"status"`

	// Time constraints
	StartTime *time.Time `gorm:"index" json:"start_time"`
	EndTime   *time.Time `gorm:"index" json:"end_time"`

	// Target constraints
	TargetUserLevels []uint `gorm:"type:integer[]" json:"target_user_levels"` // Empty means all levels
	MaxParticipants  *int   `json:"max_participants"`                         // Null means unlimited

	// Budget constraints
	TotalBudgetUSD   decimal.Decimal `gorm:"type:decimal(38,18);default:0" json:"total_budget_usd"`
	UsedBudgetUSD    decimal.Decimal `gorm:"type:decimal(38,18);default:0" json:"used_budget_usd"`
	DailyBudgetUSD   decimal.Decimal `gorm:"type:decimal(38,18);default:0" json:"daily_budget_usd"`
	UserBudgetUSD    decimal.Decimal `gorm:"type:decimal(38,18);default:0" json:"user_budget_usd"` // Per user limit

	// Configuration
	Config map[string]interface{} `gorm:"type:jsonb" json:"config"` // Flexible configuration

	// Relationships
	Tasks []ActivityTask `gorm:"foreignKey:CampaignID;references:ID" json:"tasks,omitempty"`
}

// TableName specifies the table name for ActivityCampaign
func (ActivityCampaign) TableName() string {
	return "activity_campaigns"
}

// IsActive checks if the campaign is currently active
func (ac *ActivityCampaign) IsActive() bool {
	if ac.Status != CampaignStatusActive {
		return false
	}

	now := time.Now()
	if ac.StartTime != nil && now.Before(*ac.StartTime) {
		return false
	}
	if ac.EndTime != nil && now.After(*ac.EndTime) {
		return false
	}

	return true
}

// HasBudgetRemaining checks if the campaign has budget remaining
func (ac *ActivityCampaign) HasBudgetRemaining() bool {
	if ac.TotalBudgetUSD.IsZero() {
		return true // Unlimited budget
	}
	return ac.UsedBudgetUSD.LessThan(ac.TotalBudgetUSD)
}

// CanUserParticipate checks if a user can participate in this campaign
func (ac *ActivityCampaign) CanUserParticipate(userLevel uint) bool {
	if !ac.IsActive() {
		return false
	}

	// Check user level constraints
	if len(ac.TargetUserLevels) > 0 {
		found := false
		for _, level := range ac.TargetUserLevels {
			if level == userLevel {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	return ac.HasBudgetRemaining()
}
