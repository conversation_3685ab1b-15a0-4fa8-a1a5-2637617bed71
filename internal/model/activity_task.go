package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// TaskType defines the type of task
type TaskType string

const (
	TaskTypeOneTime   TaskType = "ONE_TIME"   // Complete once
	TaskTypeDaily     TaskType = "DAILY"      // Complete daily
	TaskTypeWeekly    TaskType = "WEEKLY"     // Complete weekly
	TaskTypeMonthly   TaskType = "MONTHLY"    // Complete monthly
	TaskTypeCumulative TaskType = "CUMULATIVE" // Accumulative progress
)

// RewardType defines how rewards are calculated
type RewardType string

const (
	RewardTypeFixed      RewardType = "FIXED"      // Fixed amount per completion
	RewardTypePercentage RewardType = "PERCENTAGE" // Percentage of volume/value
	RewardTypeTiered     RewardType = "TIERED"     // Different rates for different tiers
	RewardTypeProgressive RewardType = "PROGRESSIVE" // Increasing rewards over time
)

// ActivityTask represents a configurable task within a campaign
type ActivityTask struct {
	ID        uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	CreatedAt time.Time `gorm:"index;not null" json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Task basic info
	CampaignID  uuid.UUID `gorm:"type:uuid;not null;index" json:"campaign_id"`
	Name        string    `gorm:"type:varchar(255);not null" json:"name"`
	Description string    `gorm:"type:text" json:"description"`
	TaskType    TaskType  `gorm:"type:varchar(20);not null" json:"task_type"`

	// Activity constraints
	ActivityTypes []ActivityEventType `gorm:"type:varchar(20)[]" json:"activity_types"` // Which activities count
	MinVolume     decimal.Decimal     `gorm:"type:decimal(38,18);default:0" json:"min_volume"`
	MinQuantity   int64               `gorm:"default:1" json:"min_quantity"`

	// Completion requirements
	RequiredCount   int64           `gorm:"default:1" json:"required_count"`   // How many times to complete
	RequiredVolume  decimal.Decimal `gorm:"type:decimal(38,18);default:0" json:"required_volume"`
	TimeWindowHours int             `gorm:"default:24" json:"time_window_hours"` // Time window for completion

	// Reward configuration
	RewardType        RewardType      `gorm:"type:varchar(20);not null" json:"reward_type"`
	BaseRewardUSD     decimal.Decimal `gorm:"type:decimal(38,18);default:0" json:"base_reward_usd"`
	RewardPercentage  decimal.Decimal `gorm:"type:decimal(10,6);default:0" json:"reward_percentage"`
	MaxRewardUSD      decimal.Decimal `gorm:"type:decimal(38,18);default:0" json:"max_reward_usd"`
	BonusMultiplier   decimal.Decimal `gorm:"type:decimal(10,6);default:1" json:"bonus_multiplier"`

	// Advanced configuration
	Rules  map[string]interface{} `gorm:"type:jsonb" json:"rules"`  // Custom rules
	Config map[string]interface{} `gorm:"type:jsonb" json:"config"` // Task-specific config

	// Status
	IsActive bool `gorm:"default:true;index" json:"is_active"`
	Priority int  `gorm:"default:0" json:"priority"` // Higher priority tasks processed first

	// Relationships
	Campaign ActivityCampaign `gorm:"foreignKey:CampaignID;references:ID" json:"campaign,omitempty"`
}

// TableName specifies the table name for ActivityTask
func (ActivityTask) TableName() string {
	return "activity_tasks"
}

// MatchesEvent checks if an event matches this task's criteria
func (at *ActivityTask) MatchesEvent(event *ActivityEvent) bool {
	if !at.IsActive {
		return false
	}

	// Check activity type
	if len(at.ActivityTypes) > 0 {
		found := false
		for _, activityType := range at.ActivityTypes {
			if activityType == event.EventType {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Check minimum volume
	if !at.MinVolume.IsZero() && event.Volume.LessThan(at.MinVolume) {
		return false
	}

	// Check minimum quantity
	if event.Quantity < at.MinQuantity {
		return false
	}

	return true
}

// CalculateReward calculates the reward for completing this task
func (at *ActivityTask) CalculateReward(volume decimal.Decimal, quantity int64, multiplier decimal.Decimal) decimal.Decimal {
	var reward decimal.Decimal

	switch at.RewardType {
	case RewardTypeFixed:
		reward = at.BaseRewardUSD
	case RewardTypePercentage:
		reward = volume.Mul(at.RewardPercentage)
	case RewardTypeTiered:
		// Implement tiered logic based on rules
		reward = at.calculateTieredReward(volume, quantity)
	case RewardTypeProgressive:
		// Implement progressive logic based on rules
		reward = at.calculateProgressiveReward(volume, quantity)
	default:
		reward = at.BaseRewardUSD
	}

	// Apply bonus multiplier
	reward = reward.Mul(at.BonusMultiplier).Mul(multiplier)

	// Apply max reward limit
	if !at.MaxRewardUSD.IsZero() && reward.GreaterThan(at.MaxRewardUSD) {
		reward = at.MaxRewardUSD
	}

	return reward
}

// calculateTieredReward calculates reward based on tiers
func (at *ActivityTask) calculateTieredReward(volume decimal.Decimal, quantity int64) decimal.Decimal {
	// Implementation depends on rules configuration
	// This is a placeholder for tiered reward logic
	return at.BaseRewardUSD
}

// calculateProgressiveReward calculates progressive reward
func (at *ActivityTask) calculateProgressiveReward(volume decimal.Decimal, quantity int64) decimal.Decimal {
	// Implementation depends on rules configuration
	// This is a placeholder for progressive reward logic
	return at.BaseRewardUSD
}
