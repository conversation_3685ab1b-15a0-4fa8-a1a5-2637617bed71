package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// LedgerEntryType defines the type of ledger entry
type LedgerEntryType string

const (
	LedgerEntryCredit   LedgerEntryType = "CREDIT"   // Reward earned
	LedgerEntryDebit    LedgerEntryType = "DEBIT"    // Reward spent/claimed
	LedgerEntryReserve  LedgerEntryType = "RESERVE"  // Temporary hold
	LedgerEntryRelease  LedgerEntryType = "RELEASE"  // Release reserved amount
)

// LedgerEntryStatus defines the status of a ledger entry
type LedgerEntryStatus string

const (
	LedgerStatusPending  LedgerEntryStatus = "PENDING"  // Awaiting processing
	LedgerStatusSettled  LedgerEntryStatus = "SETTLED"  // Confirmed and final
	LedgerStatusExpired  LedgerEntryStatus = "EXPIRED"  // Expired before settlement
	LedgerStatusReversed LedgerEntryStatus = "REVERSED" // Reversed/cancelled
	LedgerStatusFailed   LedgerEntryStatus = "FAILED"   // Processing failed
)

// RewardLedger represents the ledger system for all reward transactions
type RewardLedger struct {
	ID        uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	CreatedAt time.Time `gorm:"index;not null" json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Idempotency and deduplication
	IdempotencyKey string `gorm:"type:varchar(255);not null;unique;index" json:"idempotency_key"`
	ExternalRef    string `gorm:"type:varchar(255);not null;index" json:"external_ref"`

	// User information
	UserID      uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`
	UserAddress string    `gorm:"type:varchar(100);not null;index" json:"user_address"`

	// Entry details
	EntryType LedgerEntryType   `gorm:"type:varchar(20);not null;index" json:"entry_type"`
	Status    LedgerEntryStatus `gorm:"type:varchar(20);not null;index" json:"status"`

	// Amount information
	AmountUSD decimal.Decimal `gorm:"type:decimal(38,18);not null" json:"amount_usd"`
	AmountSOL decimal.Decimal `gorm:"type:decimal(38,18);not null" json:"amount_sol"`
	SolPrice  decimal.Decimal `gorm:"type:decimal(38,18);not null" json:"sol_price"`

	// Source information
	SourceType   string    `gorm:"type:varchar(50);not null;index" json:"source_type"` // CAMPAIGN, TASK, MANUAL, etc.
	SourceID     uuid.UUID `gorm:"type:uuid;index" json:"source_id"`                   // Campaign ID, Task ID, etc.
	ActivityEventID *uuid.UUID `gorm:"type:uuid;index" json:"activity_event_id"`      // Related activity event

	// Processing information
	ProcessedAt *time.Time `gorm:"index" json:"processed_at"`
	SettledAt   *time.Time `gorm:"index" json:"settled_at"`
	ExpiresAt   *time.Time `gorm:"index" json:"expires_at"`

	// Metadata
	Description string                 `gorm:"type:text" json:"description"`
	Metadata    map[string]interface{} `gorm:"type:jsonb" json:"metadata"`

	// Relationships
	User          User           `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
	ActivityEvent *ActivityEvent `gorm:"foreignKey:ActivityEventID;references:ID" json:"activity_event,omitempty"`
}

// TableName specifies the table name for RewardLedger
func (RewardLedger) TableName() string {
	return "reward_ledger"
}

// IsSettled checks if the ledger entry is settled
func (rl *RewardLedger) IsSettled() bool {
	return rl.Status == LedgerStatusSettled
}

// IsPending checks if the ledger entry is pending
func (rl *RewardLedger) IsPending() bool {
	return rl.Status == LedgerStatusPending
}

// IsExpired checks if the ledger entry is expired
func (rl *RewardLedger) IsExpired() bool {
	if rl.Status == LedgerStatusExpired {
		return true
	}
	if rl.ExpiresAt != nil && time.Now().After(*rl.ExpiresAt) {
		return true
	}
	return false
}

// CanSettle checks if the entry can be settled
func (rl *RewardLedger) CanSettle() bool {
	return rl.Status == LedgerStatusPending && !rl.IsExpired()
}

// Settle marks the entry as settled
func (rl *RewardLedger) Settle() {
	if rl.CanSettle() {
		rl.Status = LedgerStatusSettled
		now := time.Now()
		rl.SettledAt = &now
		rl.UpdatedAt = now
	}
}

// Reverse marks the entry as reversed
func (rl *RewardLedger) Reverse(reason string) {
	if rl.Status == LedgerStatusPending || rl.Status == LedgerStatusSettled {
		rl.Status = LedgerStatusReversed
		rl.UpdatedAt = time.Now()
		if rl.Metadata == nil {
			rl.Metadata = make(map[string]interface{})
		}
		rl.Metadata["reversal_reason"] = reason
		rl.Metadata["reversed_at"] = time.Now()
	}
}

// Expire marks the entry as expired
func (rl *RewardLedger) Expire() {
	if rl.Status == LedgerStatusPending {
		rl.Status = LedgerStatusExpired
		rl.UpdatedAt = time.Now()
	}
}
