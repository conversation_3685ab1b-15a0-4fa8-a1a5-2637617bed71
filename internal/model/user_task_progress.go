package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// ProgressStatus defines the status of user task progress
type ProgressStatus string

const (
	ProgressStatusActive    ProgressStatus = "ACTIVE"
	ProgressStatusCompleted ProgressStatus = "COMPLETED"
	ProgressStatusExpired   ProgressStatus = "EXPIRED"
	ProgressStatusReset     ProgressStatus = "RESET"
)

// UserTaskProgress tracks user progress on specific tasks
type UserTaskProgress struct {
	ID        uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	CreatedAt time.Time `gorm:"index;not null" json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// User and task information
	UserID uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`
	TaskID uuid.UUID `gorm:"type:uuid;not null;index" json:"task_id"`

	// Progress tracking
	Status           ProgressStatus  `gorm:"type:varchar(20);not null;index" json:"status"`
	CurrentCount     int64           `gorm:"default:0" json:"current_count"`
	CurrentVolume    decimal.Decimal `gorm:"type:decimal(38,18);default:0" json:"current_volume"`
	CompletionCount  int64           `gorm:"default:0" json:"completion_count"`  // How many times completed
	TotalRewardUSD   decimal.Decimal `gorm:"type:decimal(38,18);default:0" json:"total_reward_usd"`

	// Time tracking
	PeriodStart    time.Time  `gorm:"index" json:"period_start"`
	PeriodEnd      time.Time  `gorm:"index" json:"period_end"`
	LastActivityAt *time.Time `gorm:"index" json:"last_activity_at"`
	CompletedAt    *time.Time `gorm:"index" json:"completed_at"`
	ExpiresAt      *time.Time `gorm:"index" json:"expires_at"`

	// Streak tracking (for daily/weekly tasks)
	CurrentStreak int       `gorm:"default:0" json:"current_streak"`
	BestStreak    int       `gorm:"default:0" json:"best_streak"`
	LastStreakAt  *time.Time `json:"last_streak_at"`

	// Metadata
	Metadata map[string]interface{} `gorm:"type:jsonb" json:"metadata"`

	// Relationships
	User User         `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
	Task ActivityTask `gorm:"foreignKey:TaskID;references:ID" json:"task,omitempty"`
}

// TableName specifies the table name for UserTaskProgress
func (UserTaskProgress) TableName() string {
	return "user_task_progress"
}

// IsActive checks if the progress is active
func (utp *UserTaskProgress) IsActive() bool {
	return utp.Status == ProgressStatusActive && !utp.IsExpired()
}

// IsCompleted checks if the task is completed
func (utp *UserTaskProgress) IsCompleted() bool {
	return utp.Status == ProgressStatusCompleted
}

// IsExpired checks if the progress has expired
func (utp *UserTaskProgress) IsExpired() bool {
	if utp.Status == ProgressStatusExpired {
		return true
	}
	if utp.ExpiresAt != nil && time.Now().After(*utp.ExpiresAt) {
		return true
	}
	return false
}

// CanComplete checks if the task can be completed based on requirements
func (utp *UserTaskProgress) CanComplete(task *ActivityTask) bool {
	if !utp.IsActive() {
		return false
	}

	// Check count requirement
	if task.RequiredCount > 0 && utp.CurrentCount < task.RequiredCount {
		return false
	}

	// Check volume requirement
	if !task.RequiredVolume.IsZero() && utp.CurrentVolume.LessThan(task.RequiredVolume) {
		return false
	}

	return true
}

// AddProgress adds progress to the task
func (utp *UserTaskProgress) AddProgress(volume decimal.Decimal, quantity int64) {
	utp.CurrentCount += quantity
	utp.CurrentVolume = utp.CurrentVolume.Add(volume)
	utp.UpdatedAt = time.Now()
	now := time.Now()
	utp.LastActivityAt = &now
}

// Complete marks the task as completed
func (utp *UserTaskProgress) Complete(rewardUSD decimal.Decimal) {
	utp.Status = ProgressStatusCompleted
	utp.CompletionCount++
	utp.TotalRewardUSD = utp.TotalRewardUSD.Add(rewardUSD)
	now := time.Now()
	utp.CompletedAt = &now
	utp.UpdatedAt = now
}

// Reset resets the progress for recurring tasks
func (utp *UserTaskProgress) Reset(newPeriodStart, newPeriodEnd time.Time) {
	utp.Status = ProgressStatusActive
	utp.CurrentCount = 0
	utp.CurrentVolume = decimal.Zero
	utp.PeriodStart = newPeriodStart
	utp.PeriodEnd = newPeriodEnd
	utp.CompletedAt = nil
	utp.UpdatedAt = time.Now()

	// Set expiration based on period end
	utp.ExpiresAt = &newPeriodEnd
}

// UpdateStreak updates the streak information
func (utp *UserTaskProgress) UpdateStreak() {
	now := time.Now()
	
	// Check if this continues the streak (within 24 hours of last activity)
	if utp.LastStreakAt != nil {
		timeSinceLastStreak := now.Sub(*utp.LastStreakAt)
		if timeSinceLastStreak <= 48*time.Hour { // Allow 48 hours grace period
			utp.CurrentStreak++
		} else {
			utp.CurrentStreak = 1 // Reset streak
		}
	} else {
		utp.CurrentStreak = 1
	}

	// Update best streak
	if utp.CurrentStreak > utp.BestStreak {
		utp.BestStreak = utp.CurrentStreak
	}

	utp.LastStreakAt = &now
}
